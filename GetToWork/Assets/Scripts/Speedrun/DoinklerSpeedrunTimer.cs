// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Data;
using Isto.Core.Localization;
using Isto.Core.Speedrun;
using Isto.GTW.Configuration;
using Isto.GTW.Providers;
using System;
using System.Collections;
using TMPro;
using UnityEngine;
using Zenject;

namespace Isto.GTW.Speedrun
{
    public class DoinklerSpeedrunTimer : SpeedrunTimer
    {

        // UNITY HOOKUP

        [Header("Doinkler Hookups")]
        [SerializeField] private TextMeshProUGUI _fileNameHeader;
        [SerializeField] private TextMeshProUGUI _currentLevelTimer;
        [SerializeField] private GTWDoinklerPortfolioDefinition _doinklerPortfolioDefinition;



        private LocTerm _currentLevelNameLocalized;

        // OTHER FIELDS

        private bool _isTimeOverride = false;


        // INJECTION

        private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;

        private IGameData _gameData;
        private GTWGameState _gtwGameState;
        private LocTerm.Factory _localizedStringFactory;


        [Inject]
        public void Inject(IGameData gameData, GTWGameState gameState, IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider,
            LocTerm.Factory localizedStringFactory)
        {
            _gameData = gameData;
            _gtwGameState = gameState;
            _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;
            _localizedStringFactory = localizedStringFactory;
        }

        protected override void Awake()
        {
            base.Awake();
        }

        protected override IEnumerator Start()
        {
            _fileNameHeader.color = _speedrunSettings.GetUninitializedTimerTextColor();
            _currentLevelTimer.color = _speedrunSettings.GetUninitializedTimerTextColor();
            _chapterName.color = _speedrunSettings.GetUninitializedTimerTextColor();
            yield return base.Start();
            _fileNameHeader.color = _speedrunSettings.GetNormalTimerTextColor();
            _currentLevelTimer.color = _speedrunSettings.GetNormalTimerTextColor();
            _chapterName.color = _speedrunSettings.GetNormalTimerTextColor();
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();
            LocTerm name = _gameProgress.GetGameProgressLevelName(currentLevel);
            name.LocalizeInto(_fileNameHeader);
        }

        protected override void Update()
        {
            base.Update();

            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            // For initialization issues
            if (currentLevel == -1)
                return;


            UpdateTotalWorldTime();
            UpdateCurrentLevelTime();
        }

        private void OnEnable()
        {
            RegisterEvents();
        }

        private void RegisterEvents()
        {
            Events.Subscribe(GTWEvents.DOINKLER_STAGE_COMPLETE, Events_OnStageCompleted);
            Events.Subscribe(GTWEvents.DOINKLER_STAGE_START, Events_OnStageStarted);
        }

        private void Events_OnStageStarted()
        {
            _isTimeOverride = false;
            UpdateChapterNameLocalization();
        }

        private void Events_OnStageCompleted()
        {
            _isTimeOverride = true;
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_COMPLETE, Events_OnStageCompleted);
            Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_START, Events_OnStageStarted);
        }

        protected override void UpdateChapterNameLocalization()
        {

            if (!string.IsNullOrEmpty(_gtwGameState.GameLevelDefinition.LevelName.mTerm))
            {
                _currentLevelNameLocalized = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, _gtwGameState.GameLevelDefinition.LevelName);
            }
            else
            {
                _currentLevelNameLocalized = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, _gtwGameState.GameLevelDefinition.fallbackLevelName);
            }

        }

        private void UpdateTotalWorldTime()
        {
            float totalTime = 0f;
            foreach (var gtwGameLevelDefinition in _doinklerWorldDefinitionProvider.Current.GameLevels)
            {
                totalTime += PlayerPrefs.GetFloat(gtwGameLevelDefinition.UniqueIDTotal);
            }
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();


            TimeSpan totalTimeSpan = TimeSpan.FromSeconds(GetIndividualSplit(currentLevel));
            _mainTimer.text = UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(totalTimeSpan);
        }

        private void UpdateCurrentLevelTime()
        {
            if(_isTimeOverride)
                return;

            if(_chapterName.text != _currentLevelNameLocalized.Localize())
            {
                _currentLevelNameLocalized.LocalizeInto(_chapterName);
            }

            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            float currentTimeInLevel = _gameProgress.GetGameSecondsElapsedInLevel(currentLevel);

            TimeSpan currentTime = TimeSpan.FromMilliseconds(currentTimeInLevel * 1000f);
            _currentLevelTimer.text = UnityUtils.ConvertTimespanToTotalMinutesSecondsMilliseconds(currentTime);
        }

        protected override void SetupSegmentTimer(SpeedRunSingleTime timer, int chapter)
        {
            timer.SetColorBG((chapter % 2) == 0 ? bgEven : bgOdd);
            timer.ActivateImage();

            LocTerm name = _gameProgress.GetGameProgressLevelName(chapter);
            timer.SetChapterName(name);

            bool targetTimeExists = IsTargetTimeAvailableForChapter(chapter);
            if (_displaySplitsTargets && targetTimeExists)
            {
                TimeSpan chapterTarget = TimeSpan.FromSeconds(_targetTimes[chapter]);
                timer.SetTimer(UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(chapterTarget)); // always use same format so they look even
            }
            else
            {
                timer.SetTimer(_speedrunSettings.GetDefaultTimerText());
            }

            SpeedrunSettings.SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();
            if (config.showPersonalBestDeltas)
            {
                float chapterTime = GetIndividualSplit(chapter);
                bool timeDataExists = !chapterTime.Approx(0f);
                if (targetTimeExists && timeDataExists)
                {
                    // If we're loading a saved game the time data already exists
                    float chapterSplitSeconds = GetIndividualSplit(chapter);
                    _deltasAreCumulative = config.cumulativePersonalBestDeltas;
                    AddDeltaTimeToSegmentTimer(chapterSplitSeconds, chapter, _deltasAreCumulative);
                }
                else
                {
                    // we want the timer to exist in the UI and reserve space for its info, but be invisible
                    // until the player actually finishes that chapter
                    timer.SetDiffTimer("");
                }
            }
            else
            {
                timer.HideDiffTimer();
            }
        }

        protected override float GetIndividualSplit(int progressLevel)
        {
            float totalTime = 0f;
            var temp = _doinklerPortfolioDefinition.GTWGameWorldDefinitions[progressLevel];
            foreach (var gtwGameLevelDefinition in temp.GameLevels)
            {
                totalTime += PlayerPrefs.GetFloat(gtwGameLevelDefinition.UniqueIDTotal);
            }

            if (!_isTimeOverride)
            {
                totalTime += _gameProgress.GetGameSecondsElapsedInLevel(progressLevel);
            }

            return totalTime;
        }

        protected override void SaveRecordTimes()
        {
            SpeedrunTimerData data = new SpeedrunTimerData();
            int maxLevel = _gameProgress.GetMaxGameProgressLevel();
            data.times = new float[maxLevel];
            // We used to only save the past levels (the ones you actually finished) but users were complaining.
            // Now we save the current level too... even though the current level is not finished so the data is bad.
            // People seem to reload their game after finishing which makes it seem to them like this should be saved.
            // On the other hand if this is the main use case might as well make it work like they want. (not sure...)
            for (int i = 0; i < maxLevel; i++)
            {
                var temp = _doinklerPortfolioDefinition.GTWGameWorldDefinitions[i];
                float timeTotal = 0f;
                foreach (var gtwGameLevelDefinition in temp.GameLevels)
                {
                    timeTotal += PlayerPrefs.GetFloat(gtwGameLevelDefinition.UniqueIDTotal);
                }
                data.times[i] = timeTotal;
            }

            switch (_speedrunSettings.GetPersonalBestSaveLocation())
            {
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.DoNotSave:
                    Debug.LogWarning($"Best splits currently configured not to be saved.");
                    break;
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.SaveSlot:
                    SaveRecordToSlot(data);
                    break;
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.LocalLow:
                    SaveRecordToLocalLow(data);
                    break;
                default:
                    Debug.LogError($"Best splits save location has unsupported configuration {_speedrunSettings.GetPersonalBestSaveLocation()}");
                    break;
            }
        }
    }
}