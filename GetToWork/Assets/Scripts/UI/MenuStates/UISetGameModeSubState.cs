// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Data;
using Isto.Core.Inputs;
using Isto.Core.Speedrun;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using Isto.GTW.Configuration;
using Isto.GTW.Providers;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.GTW.UI
{
    /// <summary>
    /// This is the GTW implementation for Set Game Mode sub state. This allows the player to choose a game mode for
    /// their play session.
    /// </summary>
    public class UISetGameModeSubState : CoreUISetGameModeSubState
    {
        // UNITY HOOKUP

        [Header("Confirm Dialogue")]
        [SerializeField] private UISimpleConfirmModalState _confirmationSubState;
        [SerializeField] private TextMeshProUGUI _fileTitleText;
        [SerializeField] private TextMeshProUGUI _fileSubTitleText;
        [SerializeField] private Transform _levelSelectButtonsContainer;
        [SerializeField] private Transform _folderTabButtonsContainer;
        [SerializeField] private GTWDoinklerPortfolioDefinition _doinklerPortfolioDefinition;
        [SerializeField] private GameObject _buttonPrefab;

        [Header("FolderBackground")]
        [SerializeField] private Image _folderBackground;
        [SerializeField] private Color _folderColorBeige = new Color(47, 15, 95);
        [SerializeField] private Color _folderColorBlue = new Color(221, 37, 85);
        [SerializeField] private Color _folderColorRed = new Color(1, 49, 72);

        [Header("Right Side Of Folder")]
        [SerializeField] private Image _selectedLevelImage;
        [SerializeField] private TextMeshProUGUI _selectedLevelNameLabel;
        [SerializeField] private GameObject _levelCompleteImage;
        [SerializeField] private CoreButton _doinklerPlayLevelButton;

        [Header("Bottom of Folder")]
        [SerializeField] private CoreButton _freshStartButton;

        [Header("Speedrun Settings for Doinkler Gameplay")]
        [SerializeField] SpeedrunSettings _doinklerSpeedrunSettings;

        [Header("DEV MODE ONLY")]
        [SerializeField] public bool AllLevelsUnlocked = false;


        // OTHER FIELDS

        private GTWGameLevelDefinition _selectedLevel = null;
        private MonoPushdownStateMachine _menuController;
        private List<UIGameLevelDisplay> _gameLevelDisplays;
        private int _currentWorldIndex = 0;
        private Selectable _currentSelection;


        // PROPERTIES

        private List<GTWGameWorldDefinition> _gameWorlds => _doinklerPortfolioDefinition.GTWGameWorldDefinitions;


        // INJECTION

        private DiContainer _container;
        private GTWGameState _gameState;
        private IGameData _gameData;
        private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;

        [Inject]
        public void Inject(DiContainer container, GTWGameState gamestate, IGameData gameData,
            IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider)
        {
            _container = container;
            _gameState = gamestate;
            _gameData = gameData;
            _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;
        }


        // LIFECYCLE METHODS

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

            SetupStateMachineController(controller);

            // Subscribe to input mode changes to update navigation when switching between mouse and controller
            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);

            SetupFolderTabs();
            SetupScreenVariables(0);
            SetupGameModes(0);
            StartCoroutine(SelectFirstLevelAfterTabSwitch());
        }

        public override void Exit(MonoStateMachine controller)
        {
            base.Exit(controller);

            // Unsubscribe from events
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButton(UserActions.UICANCEL) &&
                _doinklerPlayLevelButton.IsSelected &&
                _currentSelection != null)
            {
                _currentSelection.Select();
            }
            else
            {
                if (_controls.GetButtonDown(UserActions.UICANCEL))
                {
                    _menuController.ExitSubState();
                }
            }

            // Handle tab navigation with controller
            if (_controls.GetButtonDown(UserActions.UITABLEFT) || _controls.GetButtonDown(UserActions.UITABRIGHT))
            {
                HandleTabNavigation();
            }

            // If using controller, ensure we always have a highlighted item
            if (_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject == null)
            {
                HighlightFallbackSelectable();
            }

            return this;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            // When returning from a sub-state (like the confirmation dialog), ensure navigation is set up correctly
            if (_controls.UsingJoystick())
            {
                // Try to restore the previous highlight if there was one
                if (EventSystem.current.currentSelectedGameObject == null)
                {
                    HighlightFallbackSelectable();
                }
            }
        }


        // EVENT HANDLING

        private void Events_OnInputModeChanged()
        {
            // If using controller, make sure we have a selection
            if (_controls.UsingJoystick() && _currentSelection != null)
            {
                _currentSelection.Select();
            }
            else if (!_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject != null)
            {
                // Clear selection when using mouse
                EventSystem.current.SetSelectedGameObject(null);
            }
        }


        // ACCESSORS

        public void FolderTab_OnClick(int fileNumber)
        {
            // Update the current world index and setup the screen
            SetupScreenVariables(fileNumber);
            SetupGameModes(fileNumber);

            // Update the folder tabs visuals
            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();
            foreach (UIDoinklerFolderTab folderTab in folderTabs)
            {
                folderTab.NewTabActive(fileNumber);
            }

            // Update the controller navigation to include the newly selected folder tab
            StartCoroutine(UpdateFolderTabNavigation());
            SetFolderColor(fileNumber);
        }


        public void OpenLevelButtonClicked(GTWGameLevelDefinition gameLevel)
        {
            _selectedLevel = gameLevel;
            _selectedLevelImage.sprite = _selectedLevel.LevelImage;
            _selectedLevelNameLabel.text = _selectedLevel.LevelName.ToString();
            _levelCompleteImage.SetActive(_selectedLevel.IsCompleted);

            foreach (UIGameLevelDisplay gameLevelDisplay in _gameLevelDisplays)
            {
                gameLevelDisplay.NewButtonActive(gameLevel);

                // If this is the selected level and we're using a controller, highlight it
                if (_controls.UsingJoystick() &&
                    gameLevelDisplay.GetComponentInChildren<CoreButton>() != null &&
                    gameLevel == gameLevelDisplay.GameLevel)
                {
                    CoreButton levelButton = gameLevelDisplay.GetComponentInChildren<CoreButton>();
                    if (levelButton != null && levelButton.interactable)
                    {
                        _currentSelection = levelButton;
                        _currentSelection.Select();
                    }
                }
            }

            _doinklerPlayLevelButton.Select();
        }

        public void ResetProgress_ButtonClicked()
        {
            // Remember that the fresh start button was highlighted
            if (_controls.UsingJoystick() &&
                _freshStartButton != null &&
                _freshStartButton.gameObject.activeInHierarchy &&
                _freshStartButton.interactable)
            {
                // Make sure the fresh start button is highlighted
                _freshStartButton.Select();
            }

            // Set Cancel to null as we don't want to do anything on cancel
            _confirmationSubState.SetCallbacks(ConfirmationSubState_ResetTimesConfirmed, null);
            _menuController.EnterSubState(_confirmationSubState);
        }

        public void StartLevelButtonClicked()
        {
            _doinklerWorldDefinitionProvider.Current = _gameWorlds[_currentWorldIndex];
            RebindSpeedrunSettings();
            _gameState.GameLevelDefinition = _selectedLevel;
            int saveSlot = _currentWorldIndex + 1; // 0 is taken by main game
            _gameState.LoadSaveSlotMetaData(saveSlot, (gameStateData) =>
            {
                _gameData.HasSaveData(saveSlot, (dataExists) =>
                {
                    if (dataExists)
                    {
                        _gameState.LoadSaveGame(saveSlot);
                    }
                });
            });

            _gameState.SetSaveSlot(saveSlot);
            _gameState.StartGameMode(_selectedLevel.gameMode);
        }


        // OTHER METHODS

        private void SetupStateMachineController(MonoStateMachine controller)
        {
            if (controller is MonoPushdownStateMachine stateMachine)
            {
                _menuController = stateMachine;
            }
            else
            {
                Debug.LogError("Using level select menu without a push down menu controller, this won't work");
            }
        }

        private void SetupFolderTabs()
        {
            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();

            for (int i = 0; i < folderTabs.Length; i++)
            {
                folderTabs[i].Setup(this, i);
                folderTabs[i].NewTabActive(0); // The first one
            }
        }

        private void SetFolderColor(int fileNumber)
        {
            if (fileNumber == 0 || fileNumber == 1)
            {
                _folderBackground.color = _folderColorBeige;
            }
            else if (fileNumber == 2 || fileNumber == 3)
            {
                _folderBackground.color = _folderColorBlue;
            }
            else
            {
                _folderBackground.color = _folderColorRed;
            }
        }
        private void SetupScreenVariables(int worldIndex)
        {
            _fileTitleText.text = _gameWorlds[worldIndex].WorldTitle;
            _currentWorldIndex = worldIndex;
        }

        private void SetupGameModes(int worldIndex)
        {
            _gameLevelDisplays = new List<UIGameLevelDisplay>();
            List<GTWGameLevelDefinition> gameLevels = _gameWorlds[worldIndex].GameLevels;

            bool isNextLevelUnlocked = true;

            foreach (GTWGameLevelDefinition gameLevel in gameLevels)
            {
                gameLevel.Setup();
                gameLevel.IsUnlocked = isNextLevelUnlocked;
                isNextLevelUnlocked = false;

                if (gameLevel.IsCompleted || AllLevelsUnlocked)
                {
                    isNextLevelUnlocked = true;
                }
            }

            // Clear out the current children
            while (_levelSelectButtonsContainer.childCount > 0)
            {
                DestroyImmediate(_levelSelectButtonsContainer.GetChild(0).gameObject);
            }

            for (int i = 0; i < gameLevels.Count; i++)
            {
                GTWGameLevelDefinition gtwLevel = gameLevels[i];

                // This can be changed to a factory but feels unnecessary for now
                UIGameLevelDisplay gameLevelDisplay = _container.InstantiatePrefabForComponent<UIGameLevelDisplay>(_buttonPrefab, _levelSelectButtonsContainer);
                _gameLevelDisplays.Add(gameLevelDisplay);

                bool isUnlocked = false;

                // The first level of every world should be always unlocked
                if (i == 0)
                {
                    isUnlocked = true;
                }
                else if (gtwLevel.IsCompleted)
                {
                    isUnlocked = true;
                }
                // If not completed, check to see if the previous level is completed. If so, unlock it.
                else if (i > 0)
                {
                    if (gameLevels[i - 1].IsCompleted)
                    {
                        isUnlocked = true;
                    }
                }
                gtwLevel.IsUnlocked = isUnlocked;

                // Wait a frame for the component to instantiate
                StartCoroutine(SetUpGameLevelDisplay(gameLevelDisplay, gtwLevel));
            }

            // Select the first level, but maybe we'll need to remember the last level next time??
            OpenLevelButtonClicked(_gameWorlds[worldIndex].GameLevels[0]);

            // TODO this isn't working because we are setting up in a coroutine...
            _gameLevelDisplays[0].NewButtonActive(_gameWorlds[worldIndex].GameLevels[0]);

            // Setup controller navigation for unlocked levels
            StartCoroutine(SetupControllerNavigation());
        }

        private void HandleTabNavigation()
        {
            // Determine what type of item is currently highlighted by the controller
            bool isLevelHighlighted = false;
            bool isFreshStartHighlighted = false;
            bool isFolderTabHighlighted = false;

            if (_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject != null)
            {
                Selectable currentHighlight = EventSystem.current.currentSelectedGameObject.GetComponent<Selectable>();

                if (currentHighlight != null)
                {
                    // Check if the currently highlighted item is the fresh start button
                    if (_freshStartButton != null && _freshStartButton == currentHighlight)
                    {
                        isFreshStartHighlighted = true;
                    }
                    else
                    {
                        // Check if the currently highlighted item is a level button
                        foreach (UIGameLevelDisplay display in _gameLevelDisplays)
                        {
                            CoreButton levelButton = display.GetComponentInChildren<CoreButton>();
                            if (levelButton != null && levelButton == currentHighlight)
                            {
                                isLevelHighlighted = true;
                                break;
                            }
                        }

                        // If it's not a level or fresh start button, check if it's a folder tab
                        if (!isLevelHighlighted && !isFreshStartHighlighted)
                        {
                            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();
                            foreach (UIDoinklerFolderTab folderTab in folderTabs)
                            {
                                CoreButton folderButton = folderTab.GetComponentInChildren<CoreButton>();
                                if (folderButton != null && folderButton == currentHighlight)
                                {
                                    isFolderTabHighlighted = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            // Calculate the new index based on which button was pressed
            int newIndex;
            if (_controls.GetButtonDown(UserActions.UITABLEFT))
            {
                newIndex = _currentWorldIndex - 1;
                if (newIndex < 0)
                {
                    newIndex = _gameWorlds.Count - 1;
                }
            }
            else // UITABRIGHT
            {
                newIndex = _currentWorldIndex + 1;
                if (newIndex >= _gameWorlds.Count)
                {
                    newIndex = 0;
                }
            }

            // This will update the current world index and setup navigation
            FolderTab_OnClick(newIndex);

            // If a level was highlighted before switching tabs, highlight the first level in the new tab
            if (isLevelHighlighted || _doinklerPlayLevelButton.IsSelected)
            {
                StartCoroutine(SelectFirstLevelAfterTabSwitch());
            }
            // If the fresh start button was highlighted, highlight it again in the new tab
            else if (isFreshStartHighlighted)
            {
                StartCoroutine(SelectFreshStartButtonAfterTabSwitch());
            }
            // If a folder tab was highlighted, the default behavior will highlight the new folder tab
        }

        private void HighlightFallbackSelectable()
        {
            // Try to highlight a folder tab first
            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();
            if (folderTabs.Length > _currentWorldIndex && _currentWorldIndex >= 0)
            {
                CoreButton folderButton = folderTabs[_currentWorldIndex].GetComponentInChildren<CoreButton>();
                if (folderButton != null && folderButton.interactable)
                {
                    folderButton.Select();
                    return;
                }
            }

            // If no folder tab could be highlighted, try to highlight the first level
            foreach (UIGameLevelDisplay display in _gameLevelDisplays)
            {
                CoreButton levelButton = display.GetComponentInChildren<CoreButton>();
                if (levelButton != null && levelButton.interactable)
                {
                    _currentSelection = levelButton;
                    _currentSelection.Select();
                    return;
                }
            }

            // If no level could be highlighted, try to highlight the fresh start button
            if (_freshStartButton != null && _freshStartButton.gameObject.activeInHierarchy && _freshStartButton.interactable)
            {
                _freshStartButton.Select();
            }
        }

        private void ConfirmationSubState_ResetTimesConfirmed()
        {
            foreach (GTWGameWorldDefinition gtwGameWorldDefinition in _gameWorlds)
            {
                foreach (GTWGameLevelDefinition gtwGameLevelDefinition in gtwGameWorldDefinition.GameLevels)
                {
                    gtwGameLevelDefinition.ResetTotalTimeProgress();
                }
            }

            for (int i = 1; i < _gameWorlds.Count + 1; i++)
            {
                _gameData.ClearSaveData(i, (_) =>
                {
                    Debug.Log($"Cleared Save Data for saveslot {i}");
                });
            }

            RebindSpeedrunSettings();
            // SpeedrunTimerData data = new SpeedrunTimerData();
            // int maxLevel = 6;
            // data.times = new float[maxLevel];
            //
            // _gameData.SaveSpeedrunTimes(data, (_) =>
            // {
            //     Debug.Log("Cleared Speedrun Times");
            // });


            FolderTab_OnClick(_currentWorldIndex);
            _currentWorldIndex = 0;
            _selectedLevel = _gameWorlds[0].GameLevels[0];
            StartLevelButtonClicked();
        }

        private IEnumerator SetUpGameLevelDisplay(UIGameLevelDisplay gameLevelDisplay, GTWGameLevelDefinition gtwLevel)
        {
            gameLevelDisplay.Setup(gtwLevel, this);
            yield return null;
        }

        private IEnumerator SetupControllerNavigation()
        {
            // Wait a frame to ensure all game level displays are properly set up
            yield return null;

            // Get all folder tabs
            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();

            // Get all folder tab buttons
            List<CoreButton> folderButtons = new List<CoreButton>();
            for (int i = 0; i < folderTabs.Length; i++)
            {
                if (folderTabs[i].gameObject.activeInHierarchy)
                {
                    CoreButton folderButton = folderTabs[i].GetComponentInChildren<CoreButton>();
                    if (folderButton != null && folderButton.interactable)
                    {
                        folderButtons.Add(folderButton);
                    }
                }
            }

            // Create a list of level buttons (only unlocked ones)
            List<CoreButton> levelButtons = new List<CoreButton>();
            foreach (UIGameLevelDisplay display in _gameLevelDisplays)
            {
                CoreButton levelButton = display.GetComponentInChildren<CoreButton>();
                if (levelButton != null && levelButton.interactable)
                {
                    levelButtons.Add(levelButton);
                }
            }

            // Get the fresh start button if it's active and interactable
            CoreButton freshStartButton = null;
            if (_freshStartButton != null && _freshStartButton.gameObject.activeInHierarchy && _freshStartButton.interactable)
            {
                freshStartButton = _freshStartButton;
            }

            // Set up horizontal navigation between folder tabs
            if (folderButtons.Count > 0)
            {
                for (int i = 0; i < folderButtons.Count; i++)
                {
                    // Set left navigation
                    if (i > 0)
                    {
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Left, folderButtons[i - 1]);
                    }
                    else if (folderButtons.Count > 1)
                    {
                        // Loop around to the last tab
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Left, folderButtons[folderButtons.Count - 1]);
                    }

                    // Set right navigation
                    if (i < folderButtons.Count - 1)
                    {
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Right, folderButtons[i + 1]);
                    }
                    else if (folderButtons.Count > 1)
                    {
                        // Loop around to the first tab
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Right, folderButtons[0]);
                    }

                    // Set down navigation to the first level button if available
                    if (levelButtons.Count > 0)
                    {
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Down, levelButtons[0]);
                    }
                    else if (freshStartButton != null)
                    {
                        // If no level buttons, set down navigation to the fresh start button
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Down, freshStartButton);
                    }
                }
            }

            // Set up vertical navigation for level buttons
            if (levelButtons.Count > 0)
            {
                // Set up navigation between level buttons
                for (int i = 0; i < levelButtons.Count; i++)
                {
                    // Set up navigation
                    if (i > 0)
                    {
                        UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Up, levelButtons[i - 1]);
                    }
                    else
                    {
                        // Set up navigation to the currently opened folder tab
                        CoreButton activeTab = null;

                        // Find the folder tab that corresponds to the current world index
                        for (int j = 0; j < folderTabs.Length; j++)
                        {
                            if (j == _currentWorldIndex && folderTabs[j].gameObject.activeInHierarchy)
                            {
                                CoreButton folderButton = folderTabs[j].GetComponentInChildren<CoreButton>();
                                if (folderButton != null && folderButton.interactable)
                                {
                                    activeTab = folderButton;
                                    break;
                                }
                            }
                        }

                        if (activeTab != null)
                        {
                            UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Up, activeTab);
                        }
                        else if (folderButtons.Count > 0)
                        {
                            // If we couldn't find the active tab, use the first folder button as a fallback
                            UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Up, folderButtons[0]);
                        }
                    }

                    if (i < levelButtons.Count - 1)
                    {
                        UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Down, levelButtons[i + 1]);
                    }
                    else if (freshStartButton != null)
                    {
                        // Set the last level button to navigate down to the fresh start button
                        UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Down, freshStartButton);

                        // Set the fresh start button to navigate up to the last level button
                        UIUtils.SetNavigation(freshStartButton, UIUtils.NavigationDirection.Up, levelButtons[i]);
                    }
                }
            }
            else if (freshStartButton != null && folderButtons.Count > 0)
            {
                // If there are no level buttons but there is a fresh start button,
                // set up navigation between folder tabs and fresh start button
                foreach (CoreButton folderButton in folderButtons)
                {
                    UIUtils.SetNavigation(folderButton, UIUtils.NavigationDirection.Down, freshStartButton);
                }

                // Find the folder tab that corresponds to the current world index
                CoreButton activeTab = null;
                for (int i = 0; i < folderTabs.Length; i++)
                {
                    if (i == _currentWorldIndex && folderTabs[i].gameObject.activeInHierarchy)
                    {
                        CoreButton folderButton = folderTabs[i].GetComponentInChildren<CoreButton>();
                        if (folderButton != null && folderButton.interactable)
                        {
                            activeTab = folderButton;
                            break;
                        }
                    }
                }

                if (activeTab != null)
                {
                    UIUtils.SetNavigation(freshStartButton, UIUtils.NavigationDirection.Up, activeTab);
                }
                else if (folderButtons.Count > 0)
                {
                    // If we couldn't find the active tab, use the first folder button as a fallback
                    UIUtils.SetNavigation(freshStartButton, UIUtils.NavigationDirection.Up, folderButtons[0]);
                }
            }
        }

        private IEnumerator UpdateFolderTabNavigation()
        {
            // Wait a frame to ensure all UI elements are properly set up
            yield return null;

            if (_controls.UsingJoystick())
            {
                // Find the folder tab that corresponds to the current world index
                UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();

                // Make sure we have enough folder tabs and the index is valid
                if (folderTabs.Length > _currentWorldIndex && _currentWorldIndex >= 0)
                {
                    UIDoinklerFolderTab currentTab = folderTabs[_currentWorldIndex];
                    if (currentTab.gameObject.activeInHierarchy)
                    {
                        CoreButton folderButton = currentTab.GetComponentInChildren<CoreButton>();
                        if (folderButton != null && folderButton.interactable)
                        {
                            // Highlight the folder tab
                            _currentSelection = folderButton;
                            _currentSelection.Select();
                        }
                    }
                }

                // Re-setup the entire navigation to ensure proper order
                // This will set up the navigation from folder tabs to the first level item
                StartCoroutine(SetupControllerNavigation());
            }
        }

        private IEnumerator SelectFirstLevelAfterTabSwitch()
        {
            // Wait a frame to ensure all game level displays are properly set up
            yield return null;

            if (_controls.UsingJoystick())
            {
                // Find the first unlocked level in the new tab
                foreach (UIGameLevelDisplay display in _gameLevelDisplays)
                {
                    CoreButton levelButton = display.GetComponentInChildren<CoreButton>();
                    if (levelButton != null && levelButton.interactable)
                    {
                        // Highlight the first unlocked level
                        _currentSelection = levelButton;
                        _currentSelection.Select();

                        // Make sure navigation is properly set up
                        StartCoroutine(SetupControllerNavigation());
                        break;
                    }
                }
            }
        }

        private IEnumerator SelectFreshStartButtonAfterTabSwitch()
        {
            // Wait a frame to ensure all UI elements are properly set up
            yield return null;

            if (_controls.UsingJoystick())
            {
                // Highlight the fresh start button
                _freshStartButton.Select();

                // Make sure navigation is properly set up
                StartCoroutine(SetupControllerNavigation());
            }
        }

        private void RebindSpeedrunSettings()
        {
            DiContainer projectContainer = ProjectContext.Instance.Container;
            projectContainer.Rebind(typeof(SpeedrunSettings))
                .FromInstance(_doinklerSpeedrunSettings)
                .AsCached()
                .NonLazy();
            projectContainer.Inject(_gameData);
        }
    }
}