// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Localization;
using Isto.Core.UI;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Zenject;
using static Isto.Core.Speedrun.SpeedrunSettings;

namespace Isto.Core.Speedrun
{
    /// <summary>
    /// A modular UI panel that shows playtime per game chapter (or level) according to the configuration details
    /// set in your SpeedrunSettings asset. This asset is optional in a project, but you need it if you want to use
    /// the speedrun timer.
    /// Some details are configurable in the aforementioned asset.
    /// Some details are configurable by the user in the settings menu.
    /// Some settings may be limited to certain platforms or build targets. (though not as of this writing)
    /// </summary>
    public class SpeedrunTimer : MonoBehaviour
    {
        // Enum

        private enum TimeFormat { MinutesSecondsMilliseconds, HoursMinutesSeconds, DaysHoursMinutes }


        // UNITY HOOKUP

        [Header("Panel")]
        [SerializeField] private int _panelLeftPaddingPixels = 0;
        [SerializeField] private int _panelRightPaddingPixels = 0;
        [SerializeField] private int _panelTopPaddingPixels = 0;
        [SerializeField] private int _panelBottomPaddingPixels = 0;

        [Header("Main Timer")]
        [SerializeField] private RectTransform _mainTimerSection;
        [SerializeField] protected TextMeshProUGUI _mainTimer;
        [SerializeField] private TimeFormat _timeFormat = TimeFormat.HoursMinutesSeconds;


        [Header("Run Details")]
        [SerializeField] private RectTransform _runDetailsSection;
        [SerializeField] private TextMeshProUGUI _runDescription; // demo field, need to find how to define what it should show
        [SerializeField] private TextMeshProUGUI _personalFullRunBestTime;

        [Header("Level/Chapter Details")]
        [SerializeField] private RectTransform _chapterDetailsSection;
        [SerializeField] protected TextMeshProUGUI _chapterName;
        [SerializeField] protected TextMeshProUGUI _chapterBestTime;

        [Header("Times List")]
        [SerializeField] private RectTransform _fullDisplaySection;
        [FormerlySerializedAs("_timesListStandardHeight")]
        [SerializeField] private float _segmentListStandardHeight = 210f;
        [FormerlySerializedAs("_singleTimePrefab")]
        [SerializeField] private SpeedRunSingleTime _singleSegmentTimerPrefab;
        [FormerlySerializedAs("_speedrunTimesScrollRect")]
        [SerializeField] private ScrollRect _segmentTimesScrollRect;
        [FormerlySerializedAs("_timesListLayout")]
        [SerializeField] private LayoutGroup _segmentsListLayout; // parent for the single time instances

        [Header("Extra Features")]
        [SerializeField] private RectTransform _extraFeaturesSection;
        [SerializeField] private CoreButton _toggleDisplayMode;
        [SerializeField] private CoreButton _saveSplits;


        // OTHER FIELDS

        private static readonly Color SPEEDRUN_GREEN_TEXT = new Color(0.49f, 0.729f, 0.337f);

        private RectTransform _backgroundPanel;
        private LayoutGroup _mainPanelLayout;
        protected List<SpeedRunSingleTime> _segmentTimes;

        protected List<float> _targetTimes;
        protected List<float> _personalBestTimes;
        private float _totalTime = 0;
        private float _totalBestTimes = 0;

        private int _previousLevel = 0;

        protected bool _autoScroll = false;
        protected bool _autoResize = false;

        private bool _showIcons = false;
        protected bool _showUpcomingChapters = false;
        private bool _showTargetInExtraColumn = false;

        private bool _displayIndividualSplits = false;
        protected bool _displaySplitsTargets = false;
        private bool _displaySplitsDeltas = false;
        protected bool _deltasAreCumulative = false;

        // Status stuff

        private bool _toggleCoolingDown = false;
        private float _cooldownTime = 0.1f;

        private bool _loadTargetTimesPending = false;

        // for testing - temp
        // TODO: cleanup or remove
        private Coroutine _crCache = null;


        // INJECTION

        protected IGameProgressProvider _gameProgress;
        private IGameData _gameData;
        private ILocalizationProvider _locProvider;

        private UIModalChoicePopup _modalPopup;
        private GameState _gameState;

        // optional
        protected SpeedrunSettings _speedrunSettings;

        [Inject]
        public void Inject(IGameProgressProvider gameProgress, IGameData gameData, ILocalizationProvider locProvider,
            UIModalChoicePopup modalPopup, GameState gameState,
            [InjectOptional] SpeedrunSettings speedrunSettings)
        {
            _gameProgress = gameProgress;
            _gameData = gameData;
            _locProvider = locProvider;

            _modalPopup = modalPopup;
            _gameState = gameState;

            _speedrunSettings = speedrunSettings;
        }


        // LIFECYCLE

        protected virtual void Awake()
        {
            _backgroundPanel = this.GetComponent<RectTransform>();
            _mainPanelLayout = this.GetComponent<LayoutGroup>();
            _mainPanelLayout.enabled = false;

            Debug.Assert(_backgroundPanel != null, "SpeedrunTimer expects to have a RectTransform", this.gameObject);
            Debug.Assert(_mainPanelLayout != null,
                         "SpeedrunTimer expects to have a layout group at the root level",
                         this.gameObject);
            Debug.Assert(_segmentTimesScrollRect != null,
                         "SpeedrunTimer expects to have a scroll rect",
                         this.gameObject);
            Debug.Assert(_segmentTimesScrollRect.verticalScrollbar != null,
                         "SpeedrunTimer expects to have a vertical scroll bar",
                         this.gameObject);
        }

        protected virtual IEnumerator Start()
        {
            if (_speedrunSettings == null)
            {
                Debug.LogWarning($"SpeedrunTimer aborting because SpeedrunSettings were not found in the project.");
                yield break;
            }

            LoadRecordTimes();
            LoadGoldSplitTimes();

            // Forcing these settings to exist with valid values so we can avoid having to validate them during later
            // runtime logic
            int isSpeedRunEnabled = PlayerPrefs.GetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY, defaultValue: -1);
            if (isSpeedRunEnabled == -1)
            {
                int defaultSpeedrunMenuIsOn = _speedrunSettings.GetDefaultTimerIsOnSetting() ? 1 : 0;
                PlayerPrefs.SetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY, defaultSpeedrunMenuIsOn);
            }

            int configIndex = PlayerPrefs.GetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, -1);
            if (configIndex == -1)
            {
                int defaultIndex = _speedrunSettings.GetDefaultTimerConfigIndex();
                PlayerPrefs.SetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, defaultIndex);
            }

            float topToBottom = PlayerPrefs.GetFloat(SpeedrunSettings.VERTICAL_POS_PLAYER_PREFS_KEY, defaultValue: -1f);
            if (topToBottom < -0.1f)
            {
                float defaultTopToBottomRatio = _speedrunSettings.GetDefaultScreenHeightRatio();
                PlayerPrefs.SetFloat(SpeedrunSettings.VERTICAL_POS_PLAYER_PREFS_KEY, defaultTopToBottomRatio);
            }

            float leftToRight = PlayerPrefs.GetFloat(SpeedrunSettings.HORIZONTAL_POS_PLAYER_PREFS_KEY, defaultValue: -1f);
            if (leftToRight < -0.1f)
            {
                float defaultLeftToRightRatio = _speedrunSettings.GetDefaultScreenWidthRatio();
                PlayerPrefs.SetFloat(SpeedrunSettings.HORIZONTAL_POS_PLAYER_PREFS_KEY, defaultLeftToRightRatio);
            }

            _mainTimer.color = _speedrunSettings.GetUninitializedTimerTextColor();

            yield return RefreshConfiguration();

            int currentLevel;
            do
            {
                yield return null;
                currentLevel = _gameProgress.GetCurrentGameProgressLevel();
            }
            while (currentLevel < 0 || _loadTargetTimesPending);

#if UNITY_GAMECORE
            // Since Xbox loading is async, we need to wait for it to finish loading before we can
            // setup the speedrun timer
            if (GameState.LoadingFromSave)
            {
                while (GameState.CurrentlyLoading)
                {
                    yield return null;
                }
            }
#endif

            _segmentTimes = new List<SpeedRunSingleTime>();
            int maxLevel = _gameProgress.GetMaxGameProgressLevel();
            SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();
            for (int i = 0; i < maxLevel; i++)
            {
                SpeedRunSingleTime instance = GameObject.Instantiate<SpeedRunSingleTime>(_singleSegmentTimerPrefab,
                                                                                 parent: _segmentsListLayout.transform);
                _segmentTimes.Add(instance);
                SetupSegmentTimer(instance, i);
            }

            // Assume this is the starting level/chapter - ideally find a more intelligent way to detect this
            int startingLevel = 0;
            LocTerm name = _gameProgress.GetGameProgressLevelName(startingLevel);
            name.LocalizeInto(_chapterName);

            if (config.showCurrentChapterBestTime && IsTargetTimeAvailableForChapter(startingLevel))
            {
                UpdateCurrentChapterBestTime();
                _chapterBestTime.gameObject.SetActive(true);
            }
            else
            {
                _chapterBestTime.gameObject.SetActive(false);
            }

            // would be great to be able to get speedrun category maybe from settings?
            // e.g. Atrio - Story Mode - Any%
            // For now this is what I've got available to me and it looks like we're not really using this section
            string gameName = _speedrunSettings.GetGameName();
            string gameMode = GetGameModeName();
            gameMode = "No Major Glitch"; // override this for now
            string speedrunMode = "Any %"; // no real concept of defining this for the user yet..!
            string description = "";
            if (config.showGameName)
            {
                description += gameName;
                if(config.showRunCategory)
                {
                    description += " - ";
                }
            }
            if (config.showRunCategory)
            {
                description += $"{gameMode} - {speedrunMode}";
            }

            _runDescription.text = description;

            UpdateMainTimer();
            RegisterEvents();

            yield return UpdateAllTimersConfiguration();

            currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            // If time is not progressing, then the game is still waiting on something
            while (GetIndividualSplit(currentLevel).Approx(0f))
            {
                yield return new WaitForSeconds(0.1f); // don't want our start detection to be too delayed
            }

            Color c = _speedrunSettings.GetNormalTimerTextColor();
            if (_mainTimer.color != c)
            {
                _mainTimer.color = c;
            }
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        protected virtual void Update()
        {
            if (_segmentTimes == null)
                return;

            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            // For initialization issues
            if (currentLevel == -1)
                return;

            if (_previousLevel != currentLevel)
            {
                // Previous logic didn't seem to FinishPreviousChapter before moving on for the last level.
                // I think it it better for us to close out the last split normally. We'll see.
                // TODO: Make sure to test this.
                FinishPreviousChapter();

                if (currentLevel > (_previousLevel + 1))
                {
                    Debug.LogWarning("Speedrun issue: timer is more than one level late on player progress."
                                   + " Undefined outcome.");
                }

                //Used because we pause during the heartbox transition
                _previousLevel = currentLevel;

                if (_gameProgress.IsLastProgressLevel())
                {
                    // Finish!
                    //_speedRunTimes[_previousLevel].DeActivateImage();
                    _mainTimer.color = SPEEDRUN_GREEN_TEXT;
                    //_previousLevel = currentLevel;
                    return;
                }

                BeginCurrentChapter();
            }

            UpdateTimeForChapter(currentLevel);
            UpdateMainTimer();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Events.Subscribe(Events.SETTINGS_SAVED, Events_OnSettingsSaved);
            Events.Subscribe(Events.SAVE_SPEEDRUN_BUTTON_CLICKED, Events_OnSaveSpeedrunButtonClicked);
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.SETTINGS_SAVED, Events_OnSettingsSaved);
            Events.UnSubscribe(Events.SAVE_SPEEDRUN_BUTTON_CLICKED, Events_OnSaveSpeedrunButtonClicked);
        }


        private void Events_OnSaveSpeedrunButtonClicked()
        {
            ShowSavePopup();
        }

        private void Events_OnSettingsSaved()
        {
            // Settings menu has been closed, user might have changed some of them
            if (_crCache == null)
            {
                _crCache = StartCoroutine(RefreshConfiguration());
            }
        }

        public void Button_OnSaveSplits()
        {
            ShowSavePopup();
        }

        private void ModalPopup_OnConfirmSaveSplits()
        {
            SaveRecordTimes();
        }

        private void ModalPopup_OnCancelSaveSplits()
        {

        }

        public void Button_OnReloadSplits()
        {
            LoadRecordTimes();
            LoadGoldSplitTimes();
        }

        public void Button_OnToggleDisplayMode()
        {
            if (!_toggleCoolingDown)
            {
                int styleIndex = PlayerPrefs.GetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY);
                if (++styleIndex >= _speedrunSettings.GetTimerConfigsCount())
                {
                    styleIndex = 0;
                }

                PlayerPrefs.SetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, styleIndex);

                if (_crCache == null)
                {
                    _crCache = StartCoroutine(RefreshConfiguration());

                    StartCoroutine(CooldownToggle());
                }
            }
        }


        // OTHER METHODS

        protected Color bgOdd = new Color(0.1226f, 0.1226f, 0.1226f, 0f);
        protected Color bgEven = new Color(0.1226f, 0.1226f, 0.1226f, 0.4f);
        protected Color bgSelected = new Color(0.1226f, 0.1226f, 0.1226f, 1f);
        protected virtual void SetupSegmentTimer(SpeedRunSingleTime timer, int chapter)
        {
            timer.SetColorBG((chapter % 2) == 0 ? bgEven : bgOdd);
            timer.ActivateImage();

            LocTerm name = _gameProgress.GetGameProgressLevelName(chapter);
            timer.SetChapterName(name);

            bool targetTimeExists = IsTargetTimeAvailableForChapter(chapter);
            if (_displaySplitsTargets && targetTimeExists)
            {
                TimeSpan chapterTarget = TimeSpan.FromSeconds(_targetTimes[chapter]);
                timer.SetTimer(UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(chapterTarget)); // always use same format so they look even
            }
            else
            {
                timer.SetTimer(_speedrunSettings.GetDefaultTimerText());
            }

            SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();
            if (config.showPersonalBestDeltas)
            {
                float chapterTime = GetIndividualSplit(chapter);
                bool timeDataExists = !chapterTime.Approx(0f);
                if (targetTimeExists && timeDataExists)
                {
                    // If we're loading a saved game the time data already exists
                    float chapterSplitSeconds = GetIndividualSplit(chapter);
                    _deltasAreCumulative = config.cumulativePersonalBestDeltas;
                    AddDeltaTimeToSegmentTimer(chapterSplitSeconds, chapter, _deltasAreCumulative);
                }
                else
                {
                    // we want the timer to exist in the UI and reserve space for its info, but be invisible
                    // until the player actually finishes that chapter
                    timer.SetDiffTimer("");
                }
            }
            else
            {
                timer.HideDiffTimer();
            }
        }

        private string GetGameModeName()
        {
            string name;
            if (_gameState.CurrentGameMode?.NameLoc != null)
            {
                name = Loc.Get(_gameState.CurrentGameMode.NameLoc);
            }
            else if (_gameState.CurrentGameMode != null)
            {
                name = _gameState.CurrentGameMode.internalName;
            }
            else
            {
                name = "Story Mode";
            }
            return name;
        }

        private void FinishPreviousChapter()
        {
            //Some speedrunners use custom scripts to update their livesplits. based on debug.log
            //This is for them to use.
            float previousSplitSeconds = GetIndividualSplit(_previousLevel);
            TimeSpan previousSplitTimeSpan = TimeSpan.FromSeconds(previousSplitSeconds);
            string chapterName = _gameProgress.GetGameProgressLevelInternalName(_previousLevel);
            Debug.Log("Speedrun Mode- Completed:" + chapterName + ", Split: " + previousSplitTimeSpan);

            SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();
            if (config.showPersonalBestDeltas && IsTargetTimeAvailableForChapter(_previousLevel))
            {
                AddDeltaTimeToSegmentTimer(previousSplitSeconds, _previousLevel, config.cumulativePersonalBestDeltas);
            }

            _segmentTimes[_previousLevel].SetColor(_speedrunSettings.GetNormalTimerTextColor());
            _segmentTimes[_previousLevel].SetColorBG((_previousLevel % 2) == 0 ? bgEven : bgOdd);
            //_segmentTimes[_previousLevel].DeActivateImage();
        }

        protected virtual void BeginCurrentChapter()
        {
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            _segmentTimes[currentLevel].SetColorBG(bgSelected);
            //_segmentTimes[currentLevel].ActivateImage();

            if (!_showUpcomingChapters)
            {
                _segmentTimes[currentLevel].gameObject.SetActive(true);
            }

            LocTerm nextChapterName = _gameProgress.GetGameProgressLevelName(currentLevel);
            nextChapterName.LocalizeInto(_chapterName);

            SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();
            if (config.showCurrentChapterBestTime && IsTargetTimeAvailableForChapter(currentLevel))
            {
                UpdateCurrentChapterBestTime();
                _chapterBestTime.gameObject.SetActive(true);
            }
            else
            {
                _chapterBestTime.gameObject.SetActive(false);
            }

            if (_autoScroll)
            {
                AdjustScrollViewForChapter(currentLevel);
            }

            if (_autoResize)
            {
                // Maybe overkill...
                // Update loop already will call UpdateTimeForChapter and UpdateMainTimer.
                // I am moving a lot of stuff around.
                // But maybe we only need those two method calls to look good. Test it later if perf concern.
                StartCoroutine(UpdateAllTimersConfiguration());
            }
        }

        protected void AddDeltaTimeToSegmentTimer(float segmentTime, int segmentIndex, bool cumulativeDeltas = false)
        {
            // PBStyleSettings has color options for supporting gold splits, but for now, we're missing the data
            // for that, we only have one target and that is the current record time from the PB splits.
            // We only use the PB splits as targets to beat, and use below/exact/passed compared to that.
            PBStyleSettings pbsettings = _speedrunSettings.GetPersonalBestsStyleSettings();
            Color c = _speedrunSettings.GetNormalTimerTextColor();
            float targetTime = _targetTimes[segmentIndex];
            float personalBestTime = _personalBestTimes[segmentIndex];

            if(cumulativeDeltas)
            {
                segmentTime = GetCumulativeSplit(segmentIndex);
                targetTime = GetCumulativeSplitTarget(segmentIndex);
            }

            string modifier = "+";

            // I think it makes sense here for precision to be the same as what we display in the timer
            if (segmentTime.Approx(targetTime, epsilon: 0.01f))
            {
                c = pbsettings.exactTarget;
            }
            else if (segmentTime > targetTime)
            {
                c = pbsettings.passedTarget;
            }
            else if (segmentTime < personalBestTime)
            {
                c = pbsettings.belowPersonalBest;
                modifier = "-";
            }
            else if (segmentTime < targetTime)
            {
                c = pbsettings.belowTarget;
                modifier = "-";
            }

            float diff = Mathf.Abs(segmentTime - targetTime);
            TimeSpan chapterTime = TimeSpan.FromSeconds(diff);
            string diffMsg;
            if (diff >= 3600f)
            {
                diffMsg = modifier + UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(chapterTime);
            }
            else if (diff >= 60f)
            {
                diffMsg = modifier + chapterTime.ToString("mm':'ss");
            }
            else
            {
                diffMsg = modifier + chapterTime.ToString("ss");
            }
            _segmentTimes[segmentIndex].SetDiffTimer(diffMsg);
            _segmentTimes[segmentIndex].SetDiffColor(c);
        }

        protected void AdjustScrollViewForChapter(int currentLevel)
        {
            Scrollbar bar = _segmentTimesScrollRect.verticalScrollbar;

            // We won't want to move the slider until you hit 2_2_GetPlanter,
            // Increment the slider down by 00.055 (manually determined by stephen to look good)
            // Autoscroll is a temporary measure. From my understanding we'll remove the scroll bar ASAP.
            // Keep this around in the meantime so it looks OK
            int autoScrollStartingState = 9; // HeartBoxState.ID.state2_2_GetPlanter in Atrio
            if (currentLevel >= autoScrollStartingState)
            {
                bar.value = (float)(1f - ((currentLevel - autoScrollStartingState) * 0.055));
            }
            else
            {
                bar.value = 1f;
            }
        }

        private void UpdateMainTimer()
        {
            _totalTime = _gameProgress.GetTotalGameSecondsElapsedInPlaythrough();

            if (_timeFormat == TimeFormat.DaysHoursMinutes)
            {
                TimeSpan totalTime = TimeSpan.FromMinutes(_totalTime / 60f);
                _mainTimer.text = UnityUtils.ConvertTimespanToTotalDaysHoursMinutes(totalTime);
            }
            else if (_timeFormat == TimeFormat.MinutesSecondsMilliseconds)
            {
                TimeSpan totalTime = TimeSpan.FromMilliseconds(_totalTime * 1000f);
                _mainTimer.text = UnityUtils.ConvertTimespanToTotalMinutesSecondsMilliseconds(totalTime);
            }
            else
            {
                TimeSpan totalTime = TimeSpan.FromSeconds(_totalTime);
                _mainTimer.text = UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(totalTime);
            }
        }

        [ContextMenu("UpdateAllTimers")]
        private void UpdateAllTimers()
        {
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            for (int i = 0; i < _segmentTimes.Count; i++)
            {
                if (i < currentLevel)
                {
                    UpdateTimeForChapter(i);
                }
            }
        }

        protected IEnumerator UpdateAllTimersConfiguration()
        {
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            if (currentLevel == -1 || _segmentTimes == null)
                yield break;

            for (int i = 0; i < _segmentTimes.Count; i++)
            {
                UpdateTimerConfiguration(i);
            }

            if (_autoResize)
            {
                // Doesn't depend on the layout group
                FitScrollRectToContents();
            }
            else
            {
                _fullDisplaySection.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, _segmentListStandardHeight);
            }

            // Doesn't depend on the layout group
            FitPanelToVerticalContents();
            FitPanelHorizontalContents();

            _mainPanelLayout.enabled = true;
            yield return null;
            _mainPanelLayout.enabled = false;

            _segmentsListLayout.enabled = true;
            yield return null;
            _segmentsListLayout.enabled = false;

            Vector2 contentPos = _segmentTimesScrollRect.content.anchoredPosition;
            contentPos.y = 0f;
            _segmentTimesScrollRect.content.anchoredPosition = contentPos;
        }

        [ContextMenu("SetScrollBarToTop")]
        private void SetScrollBarToTop()
        {
            _segmentTimesScrollRect.verticalScrollbar.value = 1f;
        }

        // Note: most of this has do to with setup
        private void UpdateTimeForChapter(int chapter)
        {
            float stateTime;

            if (_displaySplitsTargets && IsTargetTimeAvailableForChapter(chapter))
            {
                stateTime = _targetTimes[chapter];
            }
            else if (_displaySplitsDeltas && IsTargetTimeAvailableForChapter(chapter))
            {
                stateTime = GetIndividualSplit(chapter) - _targetTimes[chapter];
            }
            else if (_displayIndividualSplits)
            {
                stateTime = GetIndividualSplit(chapter);
            }
            else
            {
                stateTime = GetCumulativeSplit(chapter);
            }

            SpeedRunSingleTime chapterTimer = _segmentTimes[chapter];

            int current = _gameProgress.GetCurrentGameProgressLevel();
            SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();

            // Colors?
            if (_displaySplitsTargets || chapter != current)
            {
                // Split targets don't get colored / inactive chapters don't get colored
                chapterTimer.SetColor(_speedrunSettings.GetNormalTimerTextColor());
            }
            else if (config.showPBColorsInSegmentTime && IsTargetTimeAvailableForChapter(chapter))
            {
                // Until we reach end of chapter, we don't setup the diff text
                // but in the meantime we can color the text if the time goes too far as a cue that it's too late already

                PBStyleSettings pbsettings = _speedrunSettings.GetPersonalBestsStyleSettings();
                Color c = _speedrunSettings.GetNormalTimerTextColor();

                float splitTime = GetIndividualSplit(chapter);
                float targetTime = _targetTimes[chapter];

                if (splitTime > targetTime)
                {
                    c = pbsettings.passedTarget;
                }

                chapterTimer.SetColor(c);
            }

            TimeSpan time = TimeSpan.FromSeconds(stateTime);
            chapterTimer.SetTimer(UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(time));
        }

        private void UpdateTimerConfiguration(int chapter)
        {
            SpeedRunSingleTime chapterTimer = _segmentTimes[chapter];

            SetupSegmentTimer(chapterTimer, chapter);

            // Not supposed to happen anymore with the way we organize our speedrun UI
            // but I'm keeping the edge case just to be safe.
            if (chapter > _gameProgress.GetMaxGameProgressLevel())
            {
                chapterTimer.SetTimer("");
                return;
            }

            int current = _gameProgress.GetCurrentGameProgressLevel();
            if (chapter > current)
            {
                if (_showUpcomingChapters)
                {
                    if (!chapterTimer.gameObject.activeSelf)
                    {
                        chapterTimer.gameObject.SetActive(true);
                        chapterTimer.SetDiffTimer(""); // Make sure it's enabled but shows nothing
                    }
                }
                else
                {
                    if (chapterTimer.gameObject.activeSelf)
                    {
                        chapterTimer.gameObject.SetActive(false);
                    }
                    return;
                }
            }

            if (chapter == current)
            {
                chapterTimer.SetColorBG(bgSelected);
                //chapterTimer.ActivateImage();
                chapterTimer.SetDiffTimer(""); // Make sure it's enabled but shows nothing
            }
            else
            {
                chapterTimer.SetColorBG((chapter % 2) == 0 ? bgEven : bgOdd);
                //chapterTimer.DeActivateImage();
            }

            if (_showIcons)
            {
                Sprite chapterIcon = _speedrunSettings.GetIcon(chapter);
                chapterTimer.SetIcon(chapterIcon);
            }
            else
            {
                chapterTimer.HideIcon();
            }

            if (_showTargetInExtraColumn && AreTargetTimesAvailable())
            {
                if (IsTargetTimeAvailableForChapter(chapter))
                {
                    float targetTime;
                    if (_displayIndividualSplits)
                    {
                        targetTime = GetIndividualSplitTarget(chapter);
                    }
                    else
                    {
                        targetTime = GetCumulativeSplitTarget(chapter);
                    }

                    TimeSpan chapterTarget = TimeSpan.FromSeconds(targetTime);
                    // always use same format so Targets look even - this will give us padding if needed
                    chapterTimer.SetTargetTimer(UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(chapterTarget));
                }
                else
                {
                    chapterTimer.SetTargetTimer("");
                }
            }
            else
            {
                chapterTimer.HideTargetTimer();
            }

            // this if goes through all the options for how we fill up the main timer for this chapter entry
            if (chapter <= current)
            {
                UpdateTimeForChapter(chapter);
            }
            else if (_displaySplitsTargets && IsTargetTimeAvailableForChapter(chapter))
            {
                TimeSpan chapterTarget = TimeSpan.FromSeconds(_targetTimes[chapter]);
                // always use same format so Targets look even - this will give us padding if needed
                chapterTimer.SetTimer(UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(chapterTarget));
            }
            else
            {
                chapterTimer.SetTimer(_speedrunSettings.GetDefaultTimerText());
            }
        }

        private void TurnOffSplitsLayout()
        {
            _segmentsListLayout.enabled = false;
        }

        protected virtual float GetIndividualSplit(int progressLevel)
        {
            return _gameProgress.GetGameSecondsElapsedInLevel(progressLevel);
        }

        private float GetCumulativeSplit(int progressLevel)
        {
            float cumulativeTime = 0;

            //Total times - this could be provided by the GameProgressProvider as well but keeping it for now cuz time
            for (int i = 0; i <= progressLevel; i++)
            {
                float individualSplit = GetIndividualSplit(i);
                cumulativeTime += individualSplit;
            }

            return cumulativeTime;
        }

        public IEnumerator CooldownToggle()
        {
            if (!_toggleCoolingDown)
            {
                _toggleCoolingDown = true;
                yield return new WaitForSeconds(_cooldownTime);
                _toggleCoolingDown = false;
            }
        }

        private IEnumerator RefreshConfiguration()
        {
            // First define what parts of the timer UI are shown or hidden
            bool isSpeedRunEnabled = PlayerPrefs.GetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY) == 1;
            SpeedrunTimerConfig configData = _speedrunSettings.GetPlayerSelectedTimerConfig();
            ApplyTimerConfig(configData);

            // Second have the sections reposition themselves
            _mainPanelLayout.enabled = true;
            yield return null;
            _mainPanelLayout.enabled = false;

            // Third update the localization text
            UpdateChapterNameLocalization();
            UpdatePersonalBestTimeDisplay();

            // Fourth re-adjust the size of the UI
            // (ui panel resize not allowed for now)


            /*
            // Fifth update the UI position on screen
            float heightRatio = PlayerPrefs.GetFloat(SpeedrunSettings.VERTICAL_POS_PLAYER_PREFS_KEY);
            float widthRatio = PlayerPrefs.GetFloat(SpeedrunSettings.HORIZONTAL_POS_PLAYER_PREFS_KEY);

            // This is a temporary override of the settings because we seem to have constant problems with the UI pos
            // in builds and we just want to lock it in place for now
            heightRatio = 0f;
            widthRatio = 0f;

            RepositionPanelFromScreenHeight(heightRatio);
            RepositionPanelFromScreenWidth(widthRatio);*/

            // Finally update the data for individual split timers
            // e.g. maybe current language changed or the icons changed
            yield return UpdateAllTimersConfiguration();

            // Cleanup
            _crCache = null;
        }

        private void ApplyTimerConfig(SpeedrunTimerConfig config)
        {
            // Update main sections
            _mainTimerSection.gameObject.SetActive(config.IsSectionShown(SpeedrunTimerSectionEnum.MAIN_TIMER));
            _runDetailsSection.gameObject.SetActive(config.IsSectionShown(SpeedrunTimerSectionEnum.RUN_DETAILS));
            _chapterDetailsSection.gameObject.SetActive(config.IsSectionShown(SpeedrunTimerSectionEnum.CHAPTER_INFO));
            _fullDisplaySection.gameObject.SetActive(config.IsSectionShown(SpeedrunTimerSectionEnum.FULL_DISPLAY));

            // doesn't depend on the layout
            FitPanelToVerticalContents();
            FitPanelHorizontalContents();

            // Update sub sections

            _runDescription.gameObject.SetActive(config.showGameName || config.showRunCategory);
            _personalFullRunBestTime.gameObject.SetActive(config.showTotalPersonalBest);

            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();
            currentLevel = Mathf.Max(currentLevel, 0);

            // Only show chapter time if we have data to put in it
            if (config.showCurrentChapterBestTime && IsTargetTimeAvailableForChapter(currentLevel))
            {
                UpdateCurrentChapterBestTime();
                _chapterBestTime.gameObject.SetActive(true);
            }
            else
            {
                _chapterBestTime.gameObject.SetActive(false);
            }

            bool showPanelExtention = false;
            if (config.showPersonalBestSaveButton)
            {
                _saveSplits?.gameObject.SetActive(true);
                showPanelExtention = true;
            }
            else
            {
                _saveSplits?.gameObject.SetActive(false);
            }

            if (config.showStyleToggleButton)
            {
                _toggleDisplayMode?.gameObject.SetActive(true);
                showPanelExtention = true;
            }
            else
            {
                _toggleDisplayMode?.gameObject.SetActive(false);
            }

            _extraFeaturesSection?.gameObject.SetActive(showPanelExtention);

            // Update config
            switch (config.segmentTimersType)
            {
                case SpeedrunFullSectionListTypeEnum.ListOfAbsoluteTimes:
                    _displayIndividualSplits = false;
                    _displaySplitsTargets = false;
                    _displaySplitsDeltas = false;
                    break;
                case SpeedrunFullSectionListTypeEnum.ListOfRelativeTimes:
                    _displayIndividualSplits = true;
                    _displaySplitsTargets = false;
                    _displaySplitsDeltas = false;
                    break;
                case SpeedrunFullSectionListTypeEnum.ListOfPersonalBestTimes:
                    _displayIndividualSplits = false;
                    _displaySplitsTargets = true;
                    _displaySplitsDeltas = false;
                    break;
                case SpeedrunFullSectionListTypeEnum.ListOfDiffsFromPersonalBest:
                    _displayIndividualSplits = false;
                    _displaySplitsTargets = false;
                    _displaySplitsDeltas = true;
                    break;
                default:
                    Debug.LogWarning($"Individual segment timer display value {config.segmentTimersType} is not supported.");
                    break;
            }

            _segmentTimesScrollRect.vertical = config.userCanScrollTimesList;

            _autoScroll = config.autoScrollTimesListOnSplitChange;
            _showIcons = config.showChapterIcons;
            _showUpcomingChapters = config.showUpcomingChapters;
            _showTargetInExtraColumn = config.showPersonalBestColumn;
            _autoResize = config.autoResizeTimesListPanel;

            _deltasAreCumulative = config.cumulativePersonalBestDeltas;
        }

        protected void UpdateCurrentChapterBestTime()
        {
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();
            currentLevel = Mathf.Max(currentLevel, 0);

            float bestTimeSeconds = _targetTimes[currentLevel];
            TimeSpan bestTimeSpan = TimeSpan.FromSeconds(bestTimeSeconds);
            _chapterBestTime.text = UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(bestTimeSpan);
        }

        private void FitScrollRectToContents()
        {
            // consider enforcing a minimum size of _timesListStandardHeight pixels

            RectTransform segmentTransform = _singleSegmentTimerPrefab.GetComponent<RectTransform>();
            float itemSize = segmentTransform.rect.height;
            int visibleItemCount = _segmentTimes.Count(x => x.gameObject.activeSelf);
            float size = visibleItemCount * itemSize;

            _fullDisplaySection.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, size);
        }

        private void FitPanelToVerticalContents()
        {
            float height = 0f;

            if (_mainTimerSection.gameObject.activeSelf)
            {
                height += _mainTimerSection.rect.height;
            }
            if (_runDetailsSection.gameObject.activeSelf)
            {
                float detailsHeight = 0f;
                if(_runDescription.gameObject.activeSelf)
                {
                    detailsHeight += 20f;
                }
                if (_personalFullRunBestTime.gameObject.activeSelf)
                {
                    detailsHeight += 20f;
                }
                _runDetailsSection.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, detailsHeight);
                height += _runDetailsSection.rect.height;
            }
            if (_chapterDetailsSection.gameObject.activeSelf)
            {
                height += _chapterDetailsSection.rect.height;
            }
            if (_fullDisplaySection.gameObject.activeSelf)
            {
                height += _fullDisplaySection.rect.height;
            }

            _backgroundPanel.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, height);
        }

        private void FitPanelHorizontalContents()
        {
            if (_segmentTimes == null)
                return;

            float width = 0f;
            width = _segmentTimes[0].GetContentsWidth();

            // current timer widget setup looks bad above this width at its pos. highly magic number and depends on prefab
            width = MathF.Min(width, 370f);

            _fullDisplaySection.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, width);
        }

        /// <summary>
        /// Set the panel's normalized position along the vertical axis.
        /// Note that this goes from top (0) to bottom (1).
        /// </summary>
        /// <param name="heightRatio">What percentage of the screen height to be placed at</param>
        private void RepositionPanelFromScreenHeight(float heightRatio)
        {
            int panelHeight = Mathf.CeilToInt(_backgroundPanel.rect.height);
            int totalReservedSpace = _panelTopPaddingPixels + panelHeight + _panelBottomPaddingPixels;
            int play = Screen.height - totalReservedSpace;
            float panelOffset = heightRatio * play;

            Vector2 anchoredPos = _backgroundPanel.anchoredPosition;
            anchoredPos.y = -(panelOffset + _panelTopPaddingPixels);
            _backgroundPanel.anchoredPosition = anchoredPos;
        }

        /// <summary>
        /// Set the panel's normalized position along the horizontal axis.
        /// Note that this goes from left (0) to right (1).
        /// </summary>
        /// <param name="widthRatio">What percentage of the screen width to be placed at</param>
        private void RepositionPanelFromScreenWidth(float widthRatio)
        {
            int panelWidth = Mathf.CeilToInt(_backgroundPanel.rect.width);
            int totalReservedSpace = _panelLeftPaddingPixels + panelWidth + _panelRightPaddingPixels;
            int play = Screen.width - totalReservedSpace;

            // left to right ratio is more natural for settings but in the UI we set it right to left
            float panelOffset = (1f - widthRatio) * play;

            Vector2 anchoredPos = _backgroundPanel.anchoredPosition;
            anchoredPos.x = -(panelOffset + _panelRightPaddingPixels);
            _backgroundPanel.anchoredPosition = anchoredPos;
        }

        private bool AreTargetTimesAvailable()
        {
            if (_targetTimes == null)
                return false;

            if (_targetTimes.Count == 0)
                return false;

            return true;
        }

        protected bool IsTargetTimeAvailableForChapter(int n)
        {
            if (_targetTimes == null)
                return false;

            // should not really happen since we save a chapter as time=0 if it has not been done
            if (_targetTimes.Count <= n)
                return false;

            // because of the mention above we do need to make sure it's not zero, which should not be possible anyway
            if (_targetTimes[n].Approx(0f, 0.01f))
                return false;

            return true;
        }

        private float GetIndividualSplitTarget(int progressLevel)
        {
            if (_targetTimes == null)
                return 0f;

            if (_targetTimes.Count <= progressLevel)
                return 0f;

            return _targetTimes[progressLevel];
        }

        private float GetCumulativeSplitTarget(int progressLevel)
        {
            if (_targetTimes == null)
                return 0f;

            if (_targetTimes.Count <= progressLevel)
                return 0f;

            float cumulativeTime = 0;

            for (int i = 0; i <= progressLevel; i++)
            {
                float individualSplit = GetIndividualSplitTarget(i);
                cumulativeTime += individualSplit;
            }

            return cumulativeTime;
        }

        private void ShowSavePopup()
        {
            PBLocKeys pbLoc = _speedrunSettings.GetPersonalBestsLocKeys();
            string title = _locProvider.GetLocalizedText(pbLoc.savePopupTitle);
            string question = _locProvider.GetLocalizedText(pbLoc.savePopupQuestion);
            string info = _locProvider.GetLocalizedText(pbLoc.savePopupInfo);
            _modalPopup.DisplayChoice(UIModalChoicePopup.ChoicePopupType.OkCancelChoice,
                             questionText: question,
                             ModalPopup_OnConfirmSaveSplits, ModalPopup_OnCancelSaveSplits,
                             optionalExtraDescription: info,
                             titleOverride: title);
        }

        protected virtual void SaveRecordTimes()
        {
            SpeedrunTimerData data = new SpeedrunTimerData();
            int maxLevel = _gameProgress.GetMaxGameProgressLevel();
            data.times = new float[maxLevel];
            // We used to only save the past levels (the ones you actually finished) but users were complaining.
            // Now we save the current level too... even though the current level is not finished so the data is bad.
            // People seem to reload their game after finishing which makes it seem to them like this should be saved.
            // On the other hand if this is the main use case might as well make it work like they want. (not sure...)
            for (int i = 0; i < _previousLevel; i++)
            {
                float time = _gameProgress.GetGameSecondsElapsedInLevel(i);
                data.times[i] = time;
            }

            switch (_speedrunSettings.GetPersonalBestSaveLocation())
            {
                case SpeedrunPersonalBestsSaveLocationEnum.DoNotSave:
                    Debug.LogWarning($"Best splits currently configured not to be saved.");
                    break;
                case SpeedrunPersonalBestsSaveLocationEnum.SaveSlot:
                    SaveRecordToSlot(data);
                    break;
                case SpeedrunPersonalBestsSaveLocationEnum.LocalLow:
                    SaveRecordToLocalLow(data);
                    break;
                default:
                    Debug.LogError($"Best splits save location has unsupported configuration {_speedrunSettings.GetPersonalBestSaveLocation()}");
                    break;
            }
        }

        protected void SaveRecordToSlot(SpeedrunTimerData data)
        {
            Debug.LogError($"Saving Splits in Save Slot not supported yet.");
        }

        protected void SaveRecordToLocalLow(SpeedrunTimerData data)
        {
            _gameData.SaveSpeedrunTimes(data, (saveResult) =>
            {
                if (saveResult == false)
                {
                    Debug.LogError($"Speedrun splits could not be saved as PB!");
                }
                else
                {
                    Debug.Log("Speedrun splits saved as new PB.");
                    ShowSaveSuccessPopup();
                }
            });
        }

        private void ShowSaveSuccessPopup()
        {
            PBLocKeys pbLoc = _speedrunSettings.GetPersonalBestsLocKeys();

            string title = _locProvider.GetLocalizedText(pbLoc.confirmPopupTitle);

#if UNITY_GAMECORE
            string details = _locProvider.GetLocalizedText(pbLoc.confirmPopupCloud);
            string extra = "";
#else
            string details = _locProvider.GetLocalizedText(pbLoc.confirmPopupFile);
            details = details.Replace("{filename}", _speedrunSettings.GetPersonalBestsFileName());
            string extra = _locProvider.GetLocalizedText(pbLoc.confirmPopupFolder);
            extra = extra.Replace("{path}", Application.persistentDataPath);
#endif

            _modalPopup.DisplayChoice(UIModalChoicePopup.ChoicePopupType.NoChoice,
                             questionText: $"{details}\n{extra}",
                             onConfirm: null, onCancel: null,
                             optionalExtraDescription: null, // this is gray text above the main info, a bit weird?
                             titleOverride: title);
        }

        [ContextMenu("LoadRecordTimes")]
        private void LoadRecordTimes()
        {
            _loadTargetTimesPending = true;
            _gameData.LoadSpeedrunTimes(
                speedrunData =>
                {
                    _loadTargetTimesPending = false;
                    if (speedrunData == null)
                    {
                        Debug.Log($"Speedrun data not found. Won't be able to show PB.");
                        _personalFullRunBestTime.text = "";
                        _personalFullRunBestTime.gameObject.SetActive(false);
                        return;
                    }

                    _totalBestTimes = 0f;
                    bool runUnfinished = false;
                    for (int i = 0; i < speedrunData.times.Length; i++)
                    {
                        if (speedrunData.times[i].Approx(0f, 0.01f))
                        {
                            runUnfinished = true;
                            break; // If we run into a missing time there should not be more of them further on.
                        }

                        _totalBestTimes += speedrunData.times[i];
                    }

                    if (runUnfinished)
                    {
                        // don't display best time stuff if you have not finished the game
                        _personalFullRunBestTime.text = "";
                        _personalFullRunBestTime.gameObject.SetActive(false);
                    }
                    else
                    {
                        UpdatePersonalBestTimeDisplay();

                        SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();
                        _personalFullRunBestTime.gameObject.SetActive(config.showTotalPersonalBest);
                    }

                    if (_totalBestTimes.Approx(0f, 0.01f))
                    {
                        // If there's litterally no time values in the file then let's behave as if there is no pb
                        _targetTimes = null;
                    }
                    else
                    {
                        _targetTimes = new List<float>(speedrunData.times);
                    }
                });
        }

        [ContextMenu("LoadGoldSplitTimes")]
        private void LoadGoldSplitTimes()
        {
            _gameData.LoadGoldSplits(
                goldSplitsData =>
                {
                    if (goldSplitsData == null)
                    {
                        Debug.Log($"Gold splits data not found. No gold splits available.");
                        _personalBestTimes = null;
                        return;
                    }

                    if (goldSplitsData.times == null || goldSplitsData.times.Length == 0)
                    {
                        Debug.Log($"Gold splits data is empty. No gold splits available.");
                        _personalBestTimes = null;
                        return;
                    }

                    _personalBestTimes = new List<float>(goldSplitsData.times);
                    Debug.Log($"Gold splits loaded successfully. {_personalBestTimes.Count} splits available.");
                });
        }

        private void UpdatePersonalBestTimeDisplay()
        {
            if (_totalBestTimes.Approx(0f, 0.01f))
                return;

            PBLocKeys pbLoc = _speedrunSettings.GetPersonalBestsLocKeys();
            string localizedBestTimeLabel = _locProvider.GetLocalizedText(pbLoc.bestRunTimeLabel);
            TimeSpan timespan = TimeSpan.FromSeconds(_totalBestTimes);
            _personalFullRunBestTime.text = localizedBestTimeLabel + UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(timespan);
        }

        protected virtual void UpdateChapterNameLocalization()
        {
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();
            if (currentLevel > 0)
            {
                LocTerm nextChapterName = _gameProgress.GetGameProgressLevelName(currentLevel);
                nextChapterName.LocalizeInto(_chapterName);
            }
        }
    }
}