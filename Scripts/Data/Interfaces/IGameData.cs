// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Data
{
    /// <summary>
    /// Interface for managers that control the saving and loading of data for all game data.
    /// </summary>
    public interface IGameData
    {
        public enum GameDataLocation { PersistentDataPath, ResourcesFolder, UserAccount }

        public GameDataLocation CurrentSaveLocation { get; }

        public int MaximumSaveSlots { get; }

        /// <summary>
        /// Looks for an available save slot with the lowest available slot number.
        /// </summary>
        /// <param name="onSlotNumberFound">Invoked with the found slot number, or -1 if we failed to find any free
        /// slot.</param>
        public void GetLowestAvailableSlotNumber(Action<int> onSlotNumberFound = null);

        /// <summary>
        /// Looks for an available save slot numbered above previousSlot. If this fails, we fall back
        /// to GetLowestAvailableSlotNumber.
        /// </summary>
        /// <param name="previousSlot">The slot we want to be above of.</param>
        /// <param name="onNextSlotFound">Invoked with the found slot number, or -1 if we failed to find any free
        /// slot.</param>
        public void GetNextAvailableSlotNumber(int previousSlot, Action<int> onNextSlotFound);

        /// <summary>
        /// Looks for the availability of any save data.
        /// </summary>
        /// <param name="onCheckComplete">Invoked when the search is complete, with the boolean true if any data was
        /// found</param>
        public void HasAnySaveData(Action<bool> onCheckComplete = null);

        /// <summary>
        /// Looks for all the existing save data.
        /// TODO: enforce that the slots are returned in ascending order to allow some optimizations?
        /// </summary>
        /// <param name="onListReady">Invoked when the search is complete, with a list of the slot numbers we
        /// found</param>
        public void GetAllExistingSaveSlots(Action<List<int>> onListReady = null);

        /// <summary>
        /// Gets how many bytes are available to create new slots.
        /// This should be used to check before trying to create a new slot.
        /// </summary>
        /// <param name="onFileSpaceObtained">Invoked with the number of bytes, or 0 if we failed to get the
        /// amount.</param>
        public void GetRemainingSaveFileSpace(Action<long> onFileSpaceObtained = null);

        /// <summary>
        /// Gets how many total bytes of data are possible for saving slots on this platform.
        /// </summary>
        /// <param name="onMaxFileSpaceObtained">Invoked with the number of bytes, or 0 if we failed to get the
        /// amount.</param>
        public void GetMaximumSaveFileSpace(Action<long> onMaxFileSpaceObtained = null);

        /// <summary>
        /// A save operation that only modifies the GameStateSaveData
        /// </summary>
        /// <param name="saveSlot">The slot to which the data belongs.</param>
        /// <param name="saveData">The data to serialize.</param>
        /// <param name="onSaveComplete">Invoked when the save attempt is complete, with the boolean true if it has
        /// succeeded</param>
        public void SaveGameMetaData(int saveSlot, GameStateSaveData saveData, Action<bool> onSaveComplete);

        /// <summary>
        /// Finds all the DataManagers in scene and saves their data.
        /// </summary>
        /// <param name="saveSlot">The slot in which to save the data.</param>
        /// <param name="onSaveComplete">Invoked when the save attempt is complete, with the boolean true if it has
        /// succeeded</param>
        public void SaveGameData(int saveSlot, bool createBackup, Action<bool> onSaveComplete = null);

        /// <summary>
        /// A load operation that only gets the GameStateSaveData
        /// </summary>
        /// <param name="saveSlot">The slot for which to get the meta data.</param>
        /// <param name="onLoadComplete">Invoked when the load attempt is complete, with the GameStateSaveData null if
        /// it failed</param>
        public void LoadGameMetaData(int saveSlot, Action<GameStateSaveData> onLoadComplete);

        /// <summary>
        /// A load operation that only gets the thumbnail
        /// </summary>
        /// <param name="slotNumber">The slot for which to get the thumbnail.</param>
        /// <param name="onThumbnailReady">Invoked when the load attempt is complete, with the Texture2D empty if
        /// it failed</param>
        public void LoadThumbnailForSaveSlot(int slotNumber, Action<Texture2D> onThumbnailReady = null);

        /// <summary>
        /// Finds all the DataManagers and loads their data.
        /// </summary>
        /// <param name="saveSlot">The slot for which to load the game data.</param>
        /// <param name="onLoadComplete">Invoked when the load attempt is complete, with the boolean true
        /// if it has succeeded</param>
        public void LoadGameData(int saveSlot, Action<bool> onLoadComplete = null);

        /// <summary>
        /// Obliterates any data from the destination save slot then copies all the files from source save slot into
        /// destination save slot.
        /// </summary>
        /// <param name="sourceSaveSlot"></param>
        /// <param name="destinationSaveSlot"></param>
        /// <param name="onDuplicationComplete">Invoked when the operation attempt is complete, with the boolean true
        /// if it has succeeded</param>
        public void DuplicateGameData(int sourceSaveSlot, int destinationSaveSlot, Action<bool> onDuplicationComplete);

        /// <summary>
        /// Gets the data from the chosen save slot without loading the game, then converts it if needed, and stores
        /// it back in the save slot.
        /// </summary>
        /// <param name="saveSlot">The slot to upgrade.</param>
        /// <param name="targetSaveVersion">The version to upgrade to.</param>
        /// <param name="onUpgradeComplete">Invoked when the upgrade attempt is complete, with the boolean true if it
        /// has succeeded.</param>
        public void UpgradeGameData(int saveSlot, string targetSaveVersion, Action<bool> onUpgradeComplete);

        public void HasSaveData(int slotNumber, Action<bool> onCheckComplete = null);

        /// <summary>
        /// Deletes all the save data files from all save slots.
        /// </summary>
        /// <param name="onClearComplete">Invoked when the deletion attempt is complete, with the boolean true if it
        /// succeeded.</param>
        public void ClearAllSaveData(Action<bool> onClearComplete = null);

        /// <summary>
        /// Deletes all the save data files from the specified save slot.
        /// </summary>
        /// <param name="slotNumber">The number of the slot to delete.</param>
        /// <param name="onClearComplete">Invoked when the deletion attempt is complete, with the boolean true if it
        /// succeeded.</param>
        public void ClearSaveData(int slotNumber, Action<bool> onClearComplete = null);

        // Control Remapping Logic

        public void HasCustomControlsData(Action<bool> onCheckComplete = null);

        public void SaveCustomControlMappings(PlayerCustomControlsSaveData controlSaveData);

        public void LoadCustomControlMappings(Action<PlayerCustomControlsSaveData> onLoadComplete);

        public void SaveSpeedrunTimes(SpeedrunTimerData timerData, Action<bool> onSaveComplete);

        public void LoadSpeedrunTimes(Action<SpeedrunTimerData> onLoadComplete);
        public void LoadGoldSplits(Action<SpeedrunTimerData> onLoadComplete);
    }
}